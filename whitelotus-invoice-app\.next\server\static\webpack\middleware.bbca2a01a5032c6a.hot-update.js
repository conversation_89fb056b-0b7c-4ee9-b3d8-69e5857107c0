"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   HARDCODED_CREDENTIALS: () => (/* binding */ HARDCODED_CREDENTIALS),\n/* harmony export */   ROUTES: () => (/* binding */ ROUTES)\n/* harmony export */ });\nconst API_CONFIG = {\n    BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api',\n    TIMEOUT: 10000,\n    RETRIES: 3\n};\nconst APP_CONFIG = {\n    JWT_SECRET: process.env.JWT_SECRET || 'your-secret-key',\n    COOKIE_NAME: 'auth-token',\n    COOKIE_MAX_AGE: 7 * 24 * 60 * 60,\n    PAGINATION: {\n        DEFAULT_LIMIT: 5,\n        MAX_LIMIT: 100\n    },\n    ISR_REVALIDATE: 60\n};\nconst ROUTES = {\n    HOME: '/',\n    LOGIN: '/login',\n    DASHBOARD: '/dashboard',\n    INVOICE_DETAILS: '/invoice',\n    CREATE_INVOICE: '/invoice/new',\n    API: {\n        LOGIN: '/login',\n        INVOICES: '/auth/invoice',\n        INVOICE_BY_ID: (id)=>`/auth/invoice/${id}`,\n        DOWNLOAD_INVOICE: (filename)=>`/invoice/${filename}`\n    }\n};\nconst HARDCODED_CREDENTIALS = {\n    EMAIL: '<EMAIL>',\n    PASSWORD: 'Pass@1234'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/config.ts\n");

/***/ })

});