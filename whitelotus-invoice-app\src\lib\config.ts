export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api',
  TIMEOUT: 10000,
  RETRIES: 3,
} as const;

export const APP_CONFIG = {
  JWT_SECRET: process.env.JWT_SECRET || 'your-secret-key',
  COOKIE_NAME: 'auth-token',
  COOKIE_MAX_AGE: 7 * 24 * 60 * 60, // 7 days in seconds
  PAGINATION: {
    DEFAULT_LIMIT: 5,
    MAX_LIMIT: 100,
  },
  ISR_REVALIDATE: 60, // seconds
} as const;

export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  INVOICE_DETAILS: '/invoice',
  CREATE_INVOICE: '/invoice/new',
  API: {
    LOGIN: '/auth/login',
    INVOICES: '/auth/invoice',
    INVOICE_BY_ID: (id: string) => `/auth/invoice/${id}`,
    DOWNLOAD_INVOICE: (filename: string) => `/invoice/${filename}`,
  },
} as const;

export const HARDCODED_CREDENTIALS = {
  EMAIL: '<EMAIL>',
  PASSWORD: 'Pass@1234',
} as const;
