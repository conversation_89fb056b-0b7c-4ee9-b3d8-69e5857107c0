{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "VwYVP59fJLWR9u26Vut5isHb/xKOqGgt8inQIUiXOmM=", "__NEXT_PREVIEW_MODE_ID": "e5d3695e94daced19175989fd0473992", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dc67d303d7025034a229a2a4af495e5bfd6ed6ae30cd51354c720f8bfce1626f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f7698909a0cfa4383610f107a49630da6b3841bb5fcda98fab5252d810fb7dc9"}}}, "functions": {}, "sortedMiddleware": ["/"]}